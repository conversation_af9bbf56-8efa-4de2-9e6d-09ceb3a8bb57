import {
  createWorkflow,
  WorkflowResponse,
  transform,
} from "@camped-ai/workflows-sdk";
import { createInventoryItemsStep } from "@camped-ai/medusa/core-flows";
import { createDateInventoryLevelStep } from "./steps/create-date-inventory-level";

export interface DateLevelInput {
  from_date: string;
  to_date: string;
  check_in_time?: string;
  check_out_time?: string;
  available_quantity: number;
  status?: "capacity" | "reserved" | "allocated" | "canceled" | "on_hold" | "maintenance";
  notes?: string;
  metadata?: Record<string, any>;
}

export interface CreateInventoryWithDateLevelsInput {
  inventory_item: {
    sku?: string;
    origin_country?: string;
    hs_code?: string;
    metadata?: Record<string, any>;
  };
  product_variant_id?: string;
  date_levels: DateLevelInput[];
}

export interface CreateInventoryWithDateLevelsOutput {
  inventory_item: {
    id: string;
    sku?: string;
    metadata?: Record<string, any>;
  };
  date_levels: Array<{
    id: string;
    inventory_item_id: string;
    from_date: string;
    to_date: string;
    available_quantity: number;
    status: string;
  }>;
}

export const createInventoryWithDateLevelsWorkflow = createWorkflow(
  {
    name: "create-inventory-with-date-levels",
    retentionTime: 86400,
    store: true,
  },
  (input: CreateInventoryWithDateLevelsInput) => {
    // Step 1: Create inventory item
    const inventoryItem = createInventoryItemsStep([input.inventory_item]);

    // Step 2: Extract inventory item ID
    const inventoryItemData = transform(
      { inventoryItem, productVariantId: input.product_variant_id },
      ({ inventoryItem, productVariantId }) => ({
        inventory_item_id: inventoryItem[0].id,
        product_variant_id: productVariantId,
        inventory_item: inventoryItem[0],
      })
    );

    // Step 3: Resolve date_levels before iteration
    const resolvedDateLevels = transform(
      { date_levels: input.date_levels },
      ({ date_levels }) => date_levels.map(dateLevel=>createDateInventoryLevelStep({
        inventory_item_id: inventoryItemData.inventory_item_id,
        product_variant_id: inventoryItemData.product_variant_id,
        from_date: dateLevel.from_date,
        to_date: dateLevel.to_date,
        check_in_time: dateLevel.check_in_time,
        check_out_time: dateLevel.check_out_time,
        available_quantity: dateLevel.available_quantity,
        status: dateLevel.status || "capacity",
        notes: dateLevel.notes,
        metadata: dateLevel.metadata || {},
      })
      )
    );

  

    // Step 5: Return final result
    return new WorkflowResponse({
      inventory_item: inventoryItemData.inventory_item,
      date_levels: [],
    });
  }
);